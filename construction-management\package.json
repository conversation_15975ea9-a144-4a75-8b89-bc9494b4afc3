{"name": "construction-management", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@supabase/supabase-js": "^2.39.0", "@supabase/ssr": "^0.1.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "lucide-react": "^0.294.0", "jspdf": "^2.5.1", "html2canvas": "^1.4.1", "recharts": "^2.8.0", "date-fns": "^2.30.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "next": "^14.0.0"}, "devDependencies": {"typescript": "^5.0.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "tailwindcss": "^3.3.0", "postcss": "^8.4.0", "autoprefixer": "^10.4.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0"}}