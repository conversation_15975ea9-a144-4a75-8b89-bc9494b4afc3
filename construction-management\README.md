# نظام إدارة المقاولات والتطوير العقاري

نظام إداري ومحاسبي شامل مخصص لشركات المقاولات والتطوير العقاري، مطور باستخدام Next.js و Supabase.

## المميزات

### الوحدات الرئيسية
- 🏠 **الصفحة الرئيسية (Dashboard)** - لوحة تحكم شاملة مع الإحصائيات والمؤشرات
- 🏗️ **إدارة المشاريع** - إدارة كاملة للمشاريع مع تتبع الحالة والميزانية
- 🏢 **مبيعات الشقق** - إدارة مبيعات الشقق وتتبع العملاء
- 👷 **المقاولين والمستخلصات** - إدارة المقاولين والمستخلصات المالية
- 🚚 **الموردين والفواتير** - إدارة الموردين وتتبع الفواتير والمدفوعات
- 🛒 **المشتريات** - إدارة طلبات الشراء والمخزون
- 🔧 **الصيانة والتشغيل** - إدارة أعمال الصيانة والتشغيل
- 📋 **المهام اليومية** - إدارة المهام والجدولة
- 📊 **التقارير المالية** - تقارير شاملة مع إمكانية الطباعة والتصدير
- 👥 **إدارة المستخدمين** - نظام صلاحيات متعدد المستويات

### الخصائص التقنية
- ✅ **دعم كامل للغة العربية** - واجهة مستخدم باللغة العربية
- 🔐 **نظام مصادقة آمن** - تسجيل دخول وإدارة صلاحيات
- 📱 **تصميم متجاوب** - يعمل على جميع الأجهزة
- 🌐 **نظام سحابي** - يعمل من أي مكان عبر المتصفح
- 📄 **تصدير PDF** - طباعة وتصدير التقارير
- 🔍 **بحث وتصفية متقدم** - في جميع الوحدات
- ⚡ **أداء عالي** - مطور بأحدث التقنيات

## التقنيات المستخدمة

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS, Headless UI
- **Backend**: Supabase (PostgreSQL, Authentication, Real-time)
- **Icons**: Heroicons, Lucide React
- **PDF Generation**: jsPDF, html2canvas
- **Charts**: Recharts
- **Deployment**: Netlify

## التثبيت والتشغيل

### المتطلبات
- Node.js 18+
- npm أو yarn
- حساب Supabase

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd construction-management
```

2. **تثبيت التبعيات**
```bash
npm install
```

3. **إعداد متغيرات البيئة**
```bash
cp .env.local.example .env.local
```

قم بتحديث الملف `.env.local` بمعلومات Supabase الخاصة بك:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

4. **إعداد قاعدة البيانات**
- قم بتشغيل SQL الموجود في `database/schema.sql` في Supabase
- تأكد من تفعيل Row Level Security

5. **تشغيل التطبيق**
```bash
npm run dev
```

التطبيق سيعمل على `http://localhost:3000`

## النشر على Netlify

### الطريقة الأولى: Git Integration
1. ادفع الكود إلى GitHub/GitLab
2. اربط المستودع مع Netlify
3. أضف متغيرات البيئة في إعدادات Netlify
4. النشر سيتم تلقائياً

### الطريقة الثانية: Manual Deploy
1. قم ببناء المشروع:
```bash
npm run build
```

2. ارفع مجلد `out` إلى Netlify

### متغيرات البيئة في Netlify
أضف المتغيرات التالية في إعدادات Netlify:
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY`

## الاستخدام

### إنشاء حساب المدير الأول
1. انتقل إلى صفحة التسجيل
2. أنشئ حساب جديد
3. في Supabase، قم بتحديث دور المستخدم إلى 'admin'

### إدارة الصلاحيات
- **Admin**: صلاحية كاملة على النظام
- **Manager**: إدارة المشاريع والتقارير
- **Accountant**: إدارة الحسابات والتقارير المالية
- **Supervisor**: مراقبة المشاريع والمهام
- **Employee**: الوصول الأساسي للمهام

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## الدعم

للحصول على الدعم:
- افتح Issue في GitHub
- راسلنا على البريد الإلكتروني

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - انظر ملف [LICENSE](LICENSE) للتفاصيل.

## الشكر والتقدير

شكر خاص لجميع المساهمين والمكتبات مفتوحة المصدر المستخدمة في هذا المشروع.

---

**تم تطوير هذا النظام خصيصاً لشركات المقاولات والتطوير العقاري في المنطقة العربية**
