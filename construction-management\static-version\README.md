# 🚀 النسخة الثابتة الجاهزة للاستخدام

## نظام إدارة المقاولات والتطوير العقاري

### ✅ ما يحتويه هذا المجلد:

- **صفحة تسجيل الدخول** مع حسابات تجريبية
- **لوحة التحكم الرئيسية** مع إحصائيات
- **صفحة إدارة المشاريع** كاملة الوظائف
- **نظام مصادقة** يعمل بـ localStorage
- **تصميم متجاوب** باللغة العربية

### 🔑 الحسابات التجريبية:

1. **مدير النظام:**
   - البريد: `<EMAIL>`
   - كلمة المرور: `admin123`

2. **مدير:**
   - البريد: `<EMAIL>`
   - كلمة المرور: `manager123`

3. **محاسب:**
   - البريد: `<EMAIL>`
   - كلمة المرور: `acc123`

### 🚀 النشر على Netlify:

1. **احذف النشر السابق** من Netlify Dashboard
2. **اسحب وأفلت مجلد `static-version`** على Netlify
3. **انتظر انتهاء الرفع** (30 ثانية)
4. **افتح الرابط** وجرب تسجيل الدخول!

### 📱 الميزات المتاحة:

#### ✅ تعمل بالكامل:
- 🔐 **تسجيل الدخول والخروج**
- 📊 **لوحة التحكم** مع إحصائيات
- 🏗️ **إدارة المشاريع** (إضافة، تعديل، حذف، بحث)
- 👤 **نظام صلاحيات** بسيط
- 📱 **تصميم متجاوب**
- 🇸🇦 **دعم كامل للعربية**

#### 🔄 قيد التطوير:
- باقي الوحدات (مبيعات، مقاولين، إلخ)
- ربط قاعدة بيانات حقيقية
- تقارير PDF

### 🎯 كيفية الاستخدام:

1. **افتح الموقع**
2. **اضغط على أي حساب تجريبي** لملء البيانات تلقائياً
3. **اضغط "تسجيل الدخول"**
4. **استكشف لوحة التحكم**
5. **جرب إدارة المشاريع**

### 🔧 التخصيص:

- **البيانات**: موجودة في ملفات JavaScript
- **التصميم**: في ملف `styles.css`
- **الوظائف**: في ملفات HTML و JavaScript

### 📞 الدعم:

هذه نسخة تجريبية تعمل بالكامل بدون قاعدة بيانات.
للحصول على النسخة الكاملة مع Supabase، راجع الملفات الأخرى في المشروع.

---

**🎉 النظام جاهز للاستخدام فوراً!**
