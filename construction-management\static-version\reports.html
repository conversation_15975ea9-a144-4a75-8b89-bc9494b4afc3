<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>التقارير المالية - نظام إدارة المقاولات</title>
    <link rel="stylesheet" href="styles.css">
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
    <style>
        .report-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .report-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e5e7eb;
        }
        
        .report-title {
            font-size: 1.5rem;
            color: #374151;
            margin: 0;
        }
        
        .report-actions {
            display: flex;
            gap: 10px;
        }
        
        .chart-container {
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8fafc;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .summary-item {
            background: #f8fafc;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        
        .summary-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 5px;
        }
        
        .summary-label {
            color: #6b7280;
            font-size: 0.9rem;
        }
        
        .printable-area {
            background: white;
            padding: 30px;
            margin: 20px 0;
        }
        
        .company-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #2563eb;
        }
        
        .company-name {
            font-size: 2rem;
            color: #2563eb;
            margin-bottom: 10px;
        }
        
        .report-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }
        
        @media print {
            body * {
                visibility: hidden;
            }
            
            .printable-area,
            .printable-area * {
                visibility: visible;
            }
            
            .printable-area {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
            }
            
            .sidebar,
            .header,
            .report-actions {
                display: none !important;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>🏗️ نظام إدارة المقاولات</h2>
                <p>والتطوير العقاري</p>
            </div>
            
            <nav class="sidebar-nav">
                <a href="dashboard.html" class="nav-item">
                    <span class="icon">📊</span>
                    الصفحة الرئيسية
                </a>
                <a href="projects.html" class="nav-item">
                    <span class="icon">🏗️</span>
                    إدارة المشاريع
                </a>
                <a href="sales.html" class="nav-item">
                    <span class="icon">🏢</span>
                    مبيعات الشقق
                </a>
                <a href="contractors.html" class="nav-item">
                    <span class="icon">👷</span>
                    المقاولين والمستخلصات
                </a>
                <a href="suppliers.html" class="nav-item">
                    <span class="icon">🚚</span>
                    الموردين والفواتير
                </a>
                <a href="purchases.html" class="nav-item">
                    <span class="icon">🛒</span>
                    المشتريات
                </a>
                <a href="maintenance.html" class="nav-item">
                    <span class="icon">🔧</span>
                    الصيانة والتشغيل
                </a>
                <a href="tasks.html" class="nav-item">
                    <span class="icon">📋</span>
                    المهام اليومية
                </a>
                <a href="reports.html" class="nav-item active">
                    <span class="icon">📊</span>
                    التقارير المالية
                </a>
                <a href="users.html" class="nav-item">
                    <span class="icon">👥</span>
                    إدارة المستخدمين
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <h1>التقارير المالية</h1>
                <div class="user-info">
                    <div class="user-avatar" id="userAvatar">أ</div>
                    <div>
                        <div id="userName">أحمد محمد</div>
                        <div style="font-size: 12px; color: #6b7280;" id="userRole">مدير النظام</div>
                    </div>
                    <button class="logout-btn" onclick="logout()">تسجيل الخروج</button>
                </div>
            </div>

            <!-- Content -->
            <div class="content">
                <!-- Report Filters -->
                <div class="card" style="margin-bottom: 30px;">
                    <div class="card-content">
                        <h3 style="margin-bottom: 20px;">فلاتر التقرير</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                            <div class="form-group">
                                <label for="reportType">نوع التقرير</label>
                                <select id="reportType" onchange="generateReport()">
                                    <option value="financial">التقرير المالي الشامل</option>
                                    <option value="sales">تقرير المبيعات</option>
                                    <option value="projects">تقرير المشاريع</option>
                                    <option value="contractors">تقرير المقاولين</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="dateFrom">من تاريخ</label>
                                <input type="date" id="dateFrom" onchange="generateReport()">
                            </div>
                            <div class="form-group">
                                <label for="dateTo">إلى تاريخ</label>
                                <input type="date" id="dateTo" onchange="generateReport()">
                            </div>
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button class="btn" onclick="generateReport()">تحديث التقرير</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Report Content -->
                <div class="report-card">
                    <div class="report-header">
                        <h2 class="report-title" id="reportTitle">التقرير المالي الشامل</h2>
                        <div class="report-actions">
                            <button class="btn" onclick="printReport()">🖨️ طباعة</button>
                            <button class="btn btn-success" onclick="exportToPDF()">📄 تصدير PDF</button>
                        </div>
                    </div>

                    <div class="printable-area" id="printableArea">
                        <!-- Company Header -->
                        <div class="company-header">
                            <div class="company-name">شركة المقاولات والتطوير العقاري</div>
                            <div style="color: #6b7280;">التقرير المالي الشامل</div>
                        </div>

                        <!-- Report Info -->
                        <div class="report-info">
                            <div>
                                <strong>تاريخ التقرير:</strong> <span id="reportDate"></span><br>
                                <strong>الفترة:</strong> <span id="reportPeriod"></span>
                            </div>
                            <div>
                                <strong>أعده:</strong> <span id="reportCreator"></span><br>
                                <strong>الوقت:</strong> <span id="reportTime"></span>
                            </div>
                        </div>

                        <!-- Summary -->
                        <div class="summary-grid" id="reportSummary">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>

                        <!-- Chart -->
                        <div class="chart-container" id="chartContainer">
                            <div style="text-align: center; color: #6b7280;">
                                <div style="font-size: 3rem; margin-bottom: 10px;">📊</div>
                                <div>الرسم البياني سيظهر هنا</div>
                            </div>
                        </div>

                        <!-- Detailed Data -->
                        <div id="detailedData">
                            <!-- سيتم ملؤها بـ JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="auth.js"></script>
    <script>
        // بيانات التقارير (محاكاة)
        const reportData = {
            financial: {
                title: 'التقرير المالي الشامل',
                summary: [
                    { label: 'إجمالي الإيرادات', value: '25,500,000 ج.م', icon: '💰' },
                    { label: 'إجمالي المصروفات', value: '18,200,000 ج.م', icon: '💸' },
                    { label: 'صافي الربح', value: '7,300,000 ج.م', icon: '📈' },
                    { label: 'هامش الربح', value: '28.6%', icon: '📊' }
                ],
                details: `
                    <h3 style="margin: 30px 0 20px 0; color: #374151;">تفاصيل الإيرادات والمصروفات</h3>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>البند</th>
                                <th>المبلغ</th>
                                <th>النسبة</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>مبيعات الشقق</td>
                                <td>22,000,000 ج.م</td>
                                <td>86.3%</td>
                            </tr>
                            <tr>
                                <td>إيرادات أخرى</td>
                                <td>3,500,000 ج.م</td>
                                <td>13.7%</td>
                            </tr>
                            <tr style="border-top: 2px solid #e5e7eb;">
                                <td><strong>إجمالي الإيرادات</strong></td>
                                <td><strong>25,500,000 ج.م</strong></td>
                                <td><strong>100%</strong></td>
                            </tr>
                        </tbody>
                    </table>
                `
            },
            sales: {
                title: 'تقرير المبيعات',
                summary: [
                    { label: 'عدد الشقق المباعة', value: '85', icon: '🏢' },
                    { label: 'إجمالي المبيعات', value: '22,000,000 ج.م', icon: '💰' },
                    { label: 'متوسط سعر البيع', value: '2,588,235 ج.م', icon: '📊' },
                    { label: 'معدل النمو', value: '+15.2%', icon: '📈' }
                ],
                details: `
                    <h3 style="margin: 30px 0 20px 0; color: #374151;">تفاصيل المبيعات حسب المشروع</h3>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>المشروع</th>
                                <th>عدد الشقق</th>
                                <th>إجمالي المبيعات</th>
                                <th>متوسط السعر</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>مشروع الأندلس السكني</td>
                                <td>35</td>
                                <td>9,500,000 ج.م</td>
                                <td>2,714,286 ج.م</td>
                            </tr>
                            <tr>
                                <td>برج النيل التجاري</td>
                                <td>25</td>
                                <td>7,200,000 ج.م</td>
                                <td>2,880,000 ج.م</td>
                            </tr>
                            <tr>
                                <td>مجمع الزهراء السكني</td>
                                <td>25</td>
                                <td>5,300,000 ج.م</td>
                                <td>2,120,000 ج.م</td>
                            </tr>
                        </tbody>
                    </table>
                `
            },
            projects: {
                title: 'تقرير المشاريع',
                summary: [
                    { label: 'إجمالي المشاريع', value: '12', icon: '🏗️' },
                    { label: 'المشاريع المكتملة', value: '4', icon: '✅' },
                    { label: 'قيد التنفيذ', value: '6', icon: '🔄' },
                    { label: 'في التخطيط', value: '2', icon: '📋' }
                ],
                details: `
                    <h3 style="margin: 30px 0 20px 0; color: #374151;">حالة المشاريع</h3>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>المشروع</th>
                                <th>الحالة</th>
                                <th>نسبة الإنجاز</th>
                                <th>الميزانية</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>مشروع الأندلس السكني</td>
                                <td><span class="status-badge status-progress">قيد التنفيذ</span></td>
                                <td>75%</td>
                                <td>50,000,000 ج.م</td>
                            </tr>
                            <tr>
                                <td>برج النيل التجاري</td>
                                <td><span class="status-badge status-planning">تخطيط</span></td>
                                <td>10%</td>
                                <td>75,000,000 ج.م</td>
                            </tr>
                            <tr>
                                <td>مجمع الزهراء السكني</td>
                                <td><span class="status-badge status-completed">مكتمل</span></td>
                                <td>100%</td>
                                <td>60,000,000 ج.م</td>
                            </tr>
                        </tbody>
                    </table>
                `
            },
            contractors: {
                title: 'تقرير المقاولين',
                summary: [
                    { label: 'عدد المقاولين', value: '15', icon: '👷' },
                    { label: 'إجمالي المستخلصات', value: '12,500,000 ج.م', icon: '💰' },
                    { label: 'المستخلصات المعتمدة', value: '8', icon: '✅' },
                    { label: 'المستخلصات المعلقة', value: '4', icon: '⏳' }
                ],
                details: `
                    <h3 style="margin: 30px 0 20px 0; color: #374151;">تفاصيل المستخلصات</h3>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>المقاول</th>
                                <th>التخصص</th>
                                <th>عدد المستخلصات</th>
                                <th>إجمالي المبلغ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>شركة البناء المتقدم</td>
                                <td>أعمال الخرسانة</td>
                                <td>5</td>
                                <td>6,200,000 ج.م</td>
                            </tr>
                            <tr>
                                <td>مقاولات الإخوة للتشطيبات</td>
                                <td>التشطيبات</td>
                                <td>3</td>
                                <td>3,800,000 ج.م</td>
                            </tr>
                            <tr>
                                <td>شركة النيل للكهرباء</td>
                                <td>الكهرباء</td>
                                <td>4</td>
                                <td>2,500,000 ج.م</td>
                            </tr>
                        </tbody>
                    </table>
                `
            }
        };

        // توليد التقرير
        function generateReport() {
            const reportType = document.getElementById('reportType').value;
            const data = reportData[reportType];
            
            // تحديث العنوان
            document.getElementById('reportTitle').textContent = data.title;
            
            // تحديث معلومات التقرير
            const now = new Date();
            document.getElementById('reportDate').textContent = now.toLocaleDateString('ar-EG');
            document.getElementById('reportTime').textContent = now.toLocaleTimeString('ar-EG');
            document.getElementById('reportPeriod').textContent = getReportPeriod();
            
            const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
            document.getElementById('reportCreator').textContent = currentUser.name || 'غير محدد';
            
            // تحديث الملخص
            const summaryContainer = document.getElementById('reportSummary');
            summaryContainer.innerHTML = '';
            
            data.summary.forEach(item => {
                const summaryItem = document.createElement('div');
                summaryItem.className = 'summary-item';
                summaryItem.innerHTML = `
                    <div style="font-size: 2rem; margin-bottom: 10px;">${item.icon}</div>
                    <div class="summary-value">${item.value}</div>
                    <div class="summary-label">${item.label}</div>
                `;
                summaryContainer.appendChild(summaryItem);
            });
            
            // تحديث التفاصيل
            document.getElementById('detailedData').innerHTML = data.details;
        }

        // الحصول على فترة التقرير
        function getReportPeriod() {
            const dateFrom = document.getElementById('dateFrom').value;
            const dateTo = document.getElementById('dateTo').value;
            
            if (dateFrom && dateTo) {
                return `من ${new Date(dateFrom).toLocaleDateString('ar-EG')} إلى ${new Date(dateTo).toLocaleDateString('ar-EG')}`;
            } else {
                return 'جميع الفترات';
            }
        }

        // طباعة التقرير
        function printReport() {
            window.print();
        }

        // تصدير إلى PDF
        function exportToPDF() {
            const { jsPDF } = window.jspdf;
            const element = document.getElementById('printableArea');
            
            // إظهار رسالة تحميل
            const loadingMsg = document.createElement('div');
            loadingMsg.innerHTML = `
                <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); display: flex; align-items: center; justify-content: center; z-index: 9999;">
                    <div style="background: white; padding: 30px; border-radius: 10px; text-align: center;">
                        <div style="font-size: 2rem; margin-bottom: 15px;">📄</div>
                        <div>جاري إنشاء ملف PDF...</div>
                    </div>
                </div>
            `;
            document.body.appendChild(loadingMsg);

            html2canvas(element, {
                scale: 2,
                useCORS: true,
                allowTaint: true
            }).then(canvas => {
                const imgData = canvas.toDataURL('image/png');
                const pdf = new jsPDF('p', 'mm', 'a4');
                
                const imgWidth = 210;
                const pageHeight = 295;
                const imgHeight = (canvas.height * imgWidth) / canvas.width;
                let heightLeft = imgHeight;
                
                let position = 0;
                
                pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                heightLeft -= pageHeight;
                
                while (heightLeft >= 0) {
                    position = heightLeft - imgHeight;
                    pdf.addPage();
                    pdf.addImage(imgData, 'PNG', 0, position, imgWidth, imgHeight);
                    heightLeft -= pageHeight;
                }
                
                // تحديد اسم الملف
                const reportType = document.getElementById('reportType').value;
                const reportName = reportData[reportType].title;
                const date = new Date().toLocaleDateString('ar-EG').replace(/\//g, '-');
                const fileName = `${reportName}_${date}.pdf`;
                
                pdf.save(fileName);
                
                // إزالة رسالة التحميل
                document.body.removeChild(loadingMsg);
                
                // إظهار رسالة نجاح
                alert('تم تصدير التقرير بنجاح!');
            }).catch(error => {
                console.error('خطأ في تصدير PDF:', error);
                document.body.removeChild(loadingMsg);
                alert('حدث خطأ في تصدير التقرير. يرجى المحاولة مرة أخرى.');
            });
        }

        // تهيئة الصفحة
        window.addEventListener('load', function() {
            checkAuthAndUpdateUI();
            
            // تعيين التواريخ الافتراضية
            const today = new Date();
            const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
            
            document.getElementById('dateFrom').value = firstDay.toISOString().split('T')[0];
            document.getElementById('dateTo').value = today.toISOString().split('T')[0];
            
            // توليد التقرير الأولي
            generateReport();
        });
    </script>
</body>
</html>
