'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

export default function HomePage() {
  const router = useRouter()

  useEffect(() => {
    // Redirect to login page
    router.push('/auth/login')
  }, [router])

  return (
    <div style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      fontFamily: 'Arial, sans-serif',
      direction: 'rtl'
    }}>
      <div style={{ textAlign: 'center' }}>
        <h1 style={{ color: '#2563eb', marginBottom: '20px' }}>
          نظام إدارة المقاولات والتطوير العقاري
        </h1>
        <p style={{ color: '#666', marginBottom: '20px' }}>
          جاري التحويل إلى صفحة تسجيل الدخول...
        </p>
        <div style={{
          width: '40px',
          height: '40px',
          border: '4px solid #f3f3f3',
          borderTop: '4px solid #2563eb',
          borderRadius: '50%',
          animation: 'spin 1s linear infinite',
          margin: '0 auto'
        }}></div>
      </div>
    </div>
  )
}
