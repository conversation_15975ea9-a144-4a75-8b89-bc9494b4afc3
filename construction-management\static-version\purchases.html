<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>المشتريات - نظام إدارة المقاولات</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="dashboard-container">
        <!-- Sidebar -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h2>🏗️ نظام إدارة المقاولات</h2>
                <p>والتطوير العقاري</p>
            </div>
            
            <nav class="sidebar-nav">
                <a href="dashboard.html" class="nav-item">
                    <span class="icon">📊</span>
                    الصفحة الرئيسية
                </a>
                <a href="projects.html" class="nav-item">
                    <span class="icon">🏗️</span>
                    إدارة المشاريع
                </a>
                <a href="sales.html" class="nav-item">
                    <span class="icon">🏢</span>
                    مبيعات الشقق
                </a>
                <a href="contractors.html" class="nav-item">
                    <span class="icon">👷</span>
                    المقاولين والمستخلصات
                </a>
                <a href="suppliers.html" class="nav-item">
                    <span class="icon">🚚</span>
                    الموردين والفواتير
                </a>
                <a href="purchases.html" class="nav-item active">
                    <span class="icon">🛒</span>
                    المشتريات
                </a>
                <a href="maintenance.html" class="nav-item">
                    <span class="icon">🔧</span>
                    الصيانة والتشغيل
                </a>
                <a href="tasks.html" class="nav-item">
                    <span class="icon">📋</span>
                    المهام اليومية
                </a>
                <a href="reports.html" class="nav-item">
                    <span class="icon">📊</span>
                    التقارير المالية
                </a>
                <a href="users.html" class="nav-item">
                    <span class="icon">👥</span>
                    إدارة المستخدمين
                </a>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Header -->
            <div class="header">
                <h1>إدارة المشتريات</h1>
                <div class="user-info">
                    <div class="user-avatar" id="userAvatar">أ</div>
                    <div>
                        <div id="userName">أحمد محمد</div>
                        <div style="font-size: 12px; color: #6b7280;" id="userRole">مدير النظام</div>
                    </div>
                    <button class="logout-btn" onclick="logout()">تسجيل الخروج</button>
                </div>
            </div>

            <!-- Content -->
            <div class="content">
                <!-- Page Header -->
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 30px;">
                    <div>
                        <h2 style="color: #374151; margin-bottom: 5px;">إدارة المشتريات</h2>
                        <p style="color: #6b7280;">إدارة طلبات الشراء والمخزون</p>
                    </div>
                    <button class="btn" onclick="openModal()">
                        ➕ طلب شراء جديد
                    </button>
                </div>

                <!-- Stats Row -->
                <div class="stats-row">
                    <div class="mini-stat">
                        <div class="mini-stat-icon">📦</div>
                        <div class="mini-stat-number" id="totalPurchases">15</div>
                        <div class="mini-stat-label">إجمالي الطلبات</div>
                    </div>
                    <div class="mini-stat">
                        <div class="mini-stat-icon">⏳</div>
                        <div class="mini-stat-number" id="pendingPurchases">8</div>
                        <div class="mini-stat-label">طلبات معلقة</div>
                    </div>
                    <div class="mini-stat">
                        <div class="mini-stat-icon">✅</div>
                        <div class="mini-stat-number" id="receivedPurchases">7</div>
                        <div class="mini-stat-label">طلبات مستلمة</div>
                    </div>
                    <div class="mini-stat">
                        <div class="mini-stat-icon">💰</div>
                        <div class="mini-stat-number" id="totalValue">1.2M</div>
                        <div class="mini-stat-label">إجمالي القيمة</div>
                    </div>
                </div>

                <!-- Search and Filter -->
                <div class="card" style="margin-bottom: 20px;">
                    <div class="card-content" style="padding: 20px;">
                        <div class="search-bar">
                            <input type="text" class="search-input" placeholder="البحث في المشتريات..." id="searchInput">
                            <select class="filter-select" id="statusFilter">
                                <option value="">جميع الحالات</option>
                                <option value="ordered">مطلوب</option>
                                <option value="received">مستلم</option>
                                <option value="cancelled">ملغي</option>
                            </select>
                            <button class="btn" onclick="filterPurchases()">بحث</button>
                        </div>
                    </div>
                </div>

                <!-- Purchases Table -->
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">قائمة المشتريات (<span id="purchasesCount">6</span>)</h3>
                    </div>
                    <div class="card-content" style="padding: 0;">
                        <table class="table" id="purchasesTable">
                            <thead>
                                <tr>
                                    <th>الصنف</th>
                                    <th>المورد</th>
                                    <th>المشروع</th>
                                    <th>الكمية</th>
                                    <th>سعر الوحدة</th>
                                    <th>إجمالي المبلغ</th>
                                    <th>تاريخ الطلب</th>
                                    <th>الحالة</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody id="purchasesBody">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div id="purchaseModal" class="modal">
        <div class="modal-content" style="max-width: 600px;">
            <div class="modal-header">
                <h3 id="modalTitle">طلب شراء جديد</h3>
                <span class="close" onclick="closeModal()">&times;</span>
            </div>
            
            <form id="purchaseForm">
                <div class="form-group">
                    <label for="itemName">اسم الصنف *</label>
                    <input type="text" id="itemName" required placeholder="مثال: أسمنت أبيض">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="supplierSelect">المورد *</label>
                        <select id="supplierSelect" required>
                            <option value="">اختر المورد</option>
                            <option value="1">شركة مواد البناء المتحدة</option>
                            <option value="2">مؤسسة الحديد والصلب</option>
                            <option value="3">شركة الكهرباء الحديثة</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="projectSelect">المشروع *</label>
                        <select id="projectSelect" required>
                            <option value="">اختر المشروع</option>
                            <option value="1">مشروع الأندلس السكني</option>
                            <option value="2">برج النيل التجاري</option>
                            <option value="3">مجمع الزهراء السكني</option>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="quantity">الكمية *</label>
                        <input type="number" id="quantity" required placeholder="0" min="1">
                    </div>
                    <div class="form-group">
                        <label for="unitPrice">سعر الوحدة</label>
                        <input type="number" id="unitPrice" placeholder="0.00" step="0.01">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="totalAmount">إجمالي المبلغ</label>
                        <input type="number" id="totalAmount" placeholder="0.00" step="0.01" readonly>
                    </div>
                    <div class="form-group">
                        <label for="purchaseDate">تاريخ الطلب</label>
                        <input type="date" id="purchaseDate">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="purchaseStatus">الحالة</label>
                    <select id="purchaseStatus">
                        <option value="ordered">مطلوب</option>
                        <option value="received">مستلم</option>
                        <option value="cancelled">ملغي</option>
                    </select>
                </div>
                
                <div style="display: flex; gap: 10px; justify-content: flex-end; margin-top: 20px;">
                    <button type="button" class="btn" style="background: #6b7280;" onclick="closeModal()">إلغاء</button>
                    <button type="submit" class="btn">حفظ</button>
                </div>
            </form>
        </div>
    </div>

    <script src="auth.js"></script>
    <script>
        // بيانات المشتريات (محاكاة قاعدة البيانات)
        let purchases = [
            {
                id: 1,
                itemName: 'أسمنت أبيض',
                supplierName: 'شركة مواد البناء المتحدة',
                projectName: 'مشروع الأندلس السكني',
                quantity: 100,
                unitPrice: 150,
                totalAmount: 15000,
                purchaseDate: '2024-12-20',
                status: 'ordered'
            },
            {
                id: 2,
                itemName: 'حديد تسليح 12 مم',
                supplierName: 'مؤسسة الحديد والصلب',
                projectName: 'برج النيل التجاري',
                quantity: 50,
                unitPrice: 800,
                totalAmount: 40000,
                purchaseDate: '2024-12-19',
                status: 'received'
            },
            {
                id: 3,
                itemName: 'كابلات كهربائية',
                supplierName: 'شركة الكهرباء الحديثة',
                projectName: 'مجمع الزهراء السكني',
                quantity: 200,
                unitPrice: 25,
                totalAmount: 5000,
                purchaseDate: '2024-12-18',
                status: 'ordered'
            },
            {
                id: 4,
                itemName: 'طوب أحمر',
                supplierName: 'شركة مواد البناء المتحدة',
                projectName: 'مشروع الأندلس السكني',
                quantity: 5000,
                unitPrice: 2,
                totalAmount: 10000,
                purchaseDate: '2024-12-17',
                status: 'received'
            },
            {
                id: 5,
                itemName: 'مواسير PVC',
                supplierName: 'مؤسسة السباكة المتطورة',
                projectName: 'برج النيل التجاري',
                quantity: 150,
                unitPrice: 45,
                totalAmount: 6750,
                purchaseDate: '2024-12-16',
                status: 'ordered'
            },
            {
                id: 6,
                itemName: 'رمل ناعم',
                supplierName: 'شركة مواد البناء المتحدة',
                projectName: 'مجمع الزهراء السكني',
                quantity: 20,
                unitPrice: 300,
                totalAmount: 6000,
                purchaseDate: '2024-12-15',
                status: 'received'
            }
        ];

        let editingPurchaseId = null;

        // عرض المشتريات
        function displayPurchases(purchasesToShow = purchases) {
            const tbody = document.getElementById('purchasesBody');
            tbody.innerHTML = '';

            purchasesToShow.forEach(purchase => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>
                        <div style="font-weight: bold;">${purchase.itemName}</div>
                    </td>
                    <td>${purchase.supplierName}</td>
                    <td>${purchase.projectName}</td>
                    <td>${purchase.quantity}</td>
                    <td>${formatCurrency(purchase.unitPrice)}</td>
                    <td>${formatCurrency(purchase.totalAmount)}</td>
                    <td>${formatDate(purchase.purchaseDate)}</td>
                    <td>${getStatusBadge(purchase.status)}</td>
                    <td>
                        <button class="btn" style="padding: 5px 10px; font-size: 12px; margin-left: 5px;" onclick="editPurchase(${purchase.id})">تعديل</button>
                        <button class="btn btn-danger" style="padding: 5px 10px; font-size: 12px;" onclick="deletePurchase(${purchase.id})">حذف</button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            document.getElementById('purchasesCount').textContent = purchasesToShow.length;
            updateStats();
        }

        // تحديث الإحصائيات
        function updateStats() {
            const totalPurchases = purchases.length;
            const pendingPurchases = purchases.filter(p => p.status === 'ordered').length;
            const receivedPurchases = purchases.filter(p => p.status === 'received').length;
            const totalValue = purchases.reduce((sum, p) => sum + p.totalAmount, 0);

            document.getElementById('totalPurchases').textContent = totalPurchases;
            document.getElementById('pendingPurchases').textContent = pendingPurchases;
            document.getElementById('receivedPurchases').textContent = receivedPurchases;
            document.getElementById('totalValue').textContent = formatCurrencyShort(totalValue);
        }

        // تنسيق العملة
        function formatCurrency(amount) {
            return new Intl.NumberFormat('ar-EG').format(amount) + ' ج.م';
        }

        // تنسيق العملة مختصر
        function formatCurrencyShort(amount) {
            if (amount >= 1000000) {
                return (amount / 1000000).toFixed(1) + 'M';
            } else if (amount >= 1000) {
                return (amount / 1000).toFixed(1) + 'K';
            }
            return amount.toString();
        }

        // تنسيق التاريخ
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('ar-EG');
        }

        // شارة الحالة
        function getStatusBadge(status) {
            const statusMap = {
                ordered: { label: 'مطلوب', class: 'status-planning' },
                received: { label: 'مستلم', class: 'status-completed' },
                cancelled: { label: 'ملغي', class: 'status-hold' }
            };
            
            const statusInfo = statusMap[status] || { label: status, class: 'status-planning' };
            return `<span class="status-badge ${statusInfo.class}">${statusInfo.label}</span>`;
        }

        // فتح النافذة المنبثقة
        function openModal() {
            document.getElementById('purchaseModal').style.display = 'block';
            document.getElementById('modalTitle').textContent = 'طلب شراء جديد';
            document.getElementById('purchaseForm').reset();
            editingPurchaseId = null;
        }

        // إغلاق النافذة المنبثقة
        function closeModal() {
            document.getElementById('purchaseModal').style.display = 'none';
        }

        // تعديل مشترى
        function editPurchase(id) {
            const purchase = purchases.find(p => p.id === id);
            if (purchase) {
                editingPurchaseId = id;
                document.getElementById('modalTitle').textContent = 'تعديل طلب الشراء';
                document.getElementById('itemName').value = purchase.itemName;
                document.getElementById('quantity').value = purchase.quantity;
                document.getElementById('unitPrice').value = purchase.unitPrice;
                document.getElementById('totalAmount').value = purchase.totalAmount;
                document.getElementById('purchaseDate').value = purchase.purchaseDate || '';
                document.getElementById('purchaseStatus').value = purchase.status;
                document.getElementById('purchaseModal').style.display = 'block';
            }
        }

        // حذف مشترى
        function deletePurchase(id) {
            if (confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
                purchases = purchases.filter(p => p.id !== id);
                displayPurchases();
            }
        }

        // تصفية المشتريات
        function filterPurchases() {
            const searchTerm = document.getElementById('searchInput').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;

            let filtered = purchases;

            if (searchTerm) {
                filtered = filtered.filter(purchase =>
                    purchase.itemName.toLowerCase().includes(searchTerm) ||
                    purchase.supplierName.toLowerCase().includes(searchTerm) ||
                    purchase.projectName.toLowerCase().includes(searchTerm)
                );
            }

            if (statusFilter) {
                filtered = filtered.filter(purchase => purchase.status === statusFilter);
            }

            displayPurchases(filtered);
        }

        // حساب إجمالي المبلغ تلقائياً
        function calculateTotal() {
            const quantity = parseFloat(document.getElementById('quantity').value) || 0;
            const unitPrice = parseFloat(document.getElementById('unitPrice').value) || 0;
            const total = quantity * unitPrice;
            document.getElementById('totalAmount').value = total.toFixed(2);
        }

        // حفظ المشترى
        document.getElementById('purchaseForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const purchaseData = {
                itemName: document.getElementById('itemName').value,
                supplierName: 'شركة مواد البناء المتحدة', // يمكن ربطها بالمورد المختار
                projectName: 'مشروع الأندلس السكني', // يمكن ربطها بالمشروع المختار
                quantity: parseInt(document.getElementById('quantity').value),
                unitPrice: parseFloat(document.getElementById('unitPrice').value) || 0,
                totalAmount: parseFloat(document.getElementById('totalAmount').value) || 0,
                purchaseDate: document.getElementById('purchaseDate').value,
                status: document.getElementById('purchaseStatus').value
            };

            if (editingPurchaseId) {
                // تعديل مشترى موجود
                const index = purchases.findIndex(p => p.id === editingPurchaseId);
                if (index !== -1) {
                    purchases[index] = { ...purchases[index], ...purchaseData };
                }
            } else {
                // إضافة مشترى جديد
                const newPurchase = {
                    id: Date.now(),
                    ...purchaseData
                };
                purchases.push(newPurchase);
            }

            displayPurchases();
            closeModal();
        });

        // إضافة مستمعين للحساب التلقائي
        document.getElementById('quantity').addEventListener('input', calculateTotal);
        document.getElementById('unitPrice').addEventListener('input', calculateTotal);

        // تهيئة الصفحة
        window.addEventListener('load', function() {
            checkAuthAndUpdateUI();
            displayPurchases();

            // إضافة مستمع للبحث المباشر
            document.getElementById('searchInput').addEventListener('input', filterPurchases);
            document.getElementById('statusFilter').addEventListener('change', filterPurchases);
        });

        // إغلاق النافذة عند النقر خارجها
        window.addEventListener('click', function(e) {
            const modal = document.getElementById('purchaseModal');
            if (e.target === modal) {
                closeModal();
            }
        });

        // إضافة أنماط الإحصائيات
        const style = document.createElement('style');
        style.textContent = `
            .stats-row {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 15px;
                margin-bottom: 30px;
            }
            
            .mini-stat {
                background: white;
                padding: 20px;
                border-radius: 8px;
                text-align: center;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            
            .mini-stat-icon {
                font-size: 1.5rem;
                margin-bottom: 10px;
            }
            
            .mini-stat-number {
                font-size: 1.5rem;
                font-weight: bold;
                color: #2563eb;
                margin-bottom: 5px;
            }
            
            .mini-stat-label {
                color: #6b7280;
                font-size: 0.9rem;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
